---
grails:
    profile: web
    codegen:
        defaultPackage: ws_v2
    spring:
        transactionManagement:
            proxies: false
    gorm:
        autowire: true
        reactor:
            events: false
info:
    app:
        name: '@info.app.name@'
        version: '@info.app.version@'
        grailsVersion: '@info.app.grailsVersion@'
spring:
    main:
        banner-mode: "off"
    groovy:
        template:
            check-template-location: false

micronaut:
    application:
        name: ws_v2
    server:
        netty:
            access-logger:
                enabled: false

management:
    endpoints:
        web:
            exposure:
                include: health,info,metrics
        enabled-by-default: false
    endpoint:
        health:
            enabled: true
            show-details: when-authorized
        info:
            enabled: true
        metrics:
            enabled: true
    health:
        defaults:
            enabled: false

---
grails:
    mime:
        disable:
            accept:
                header:
                    userAgents:
                        - Gecko
                        - WebKit
                        - Presto
                        - Trident
        types:
            all: '*/*'
            atom: application/atom+xml
            css: text/css
            csv: text/csv
            form: application/x-www-form-urlencoded
            html:
                - text/html
                - application/xhtml+xml
            js: text/javascript
            json:
                - application/json
                - text/json
            multipartForm: multipart/form-data
            pdf: application/pdf
            rss: application/rss+xml
            text: text/plain
            hal:
                - application/hal+json
                - application/hal+xml
            xml:
                - text/xml
                - application/xml
    urlmapping:
        cache:
            maxsize: 1000
    controllers:
        defaultScope: singleton
        upload:
            maxFileSize: 80214400
            maxRequestSize: 80214400
    converters:
        encoding: UTF-8
    assets:
        minifyJs: true
        minifyCss: true
        enableSourceMaps: false
        excludes: ['**/*.less']
        includes: ['**/*.js', '**/*.css']
    views:
        default:
            codec: html
        gsp:
            encoding: UTF-8
            htmlcodec: xml
            codecs:
                expression: html
                scriptlets: html
                taglib: none
                staticparts: none
endpoints:
    jmx:
        unique-names: true

---
hibernate:
    cache:
        queries: false
        use_second_level_cache: false
        use_query_cache: false
        region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
    hbm2ddl:
        auto: update
    show_sql: false
    format_sql: false

dataSources:
    dataSource:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        username: wscontent
        password: wscontent
    wsuser:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        username: wsuser
        password: wsuser
    wscontent:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        username: wscontent
        password: wscontent
    wslog:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        username: wslog
        password: wslog

environments:
    development:
        dataSources:
            dataSource:
                dbCreate: update
                url: ****************************************************************************
            wsuser:
                dbCreate: update
                url: *************************************************************************
            wslog: # Add this
                dbCreate: update
                url: ************************************************************************
        grails:
            serverURL: http://localhost:8080

    test:
        dataSources:
            dataSource:
                driverClassName: org.h2.Driver
                dbCreate: create-drop
                url: jdbc:h2:mem:testDb;MVCC=TRUE;LOCK_TIMEOUT=10000;DB_CLOSE_ON_EXIT=FALSE
                username: sa
                password: ''
            wsuser:
                driverClassName: org.h2.Driver
                dbCreate: create-drop
                url: jdbc:h2:mem:testDbUser;MVCC=TRUE;LOCK_TIMEOUT=10000;DB_CLOSE_ON_EXIT=FALSE
                username: sa
                password: ''
            wscontent:
                driverClassName: org.h2.Driver
                dbCreate: create-drop
                url: jdbc:h2:mem:testDbContent;MVCC=TRUE;LOCK_TIMEOUT=10000;DB_CLOSE_ON_EXIT=FALSE
                username: sa
                password: ''
            wslog:
                driverClassName: org.h2.Driver
                dbCreate: create-drop
                url: jdbc:h2:mem:testDbLog;MVCC=TRUE;LOCK_TIMEOUT=10000;DB_CLOSE_ON_EXIT=FALSE
                username: sa
                password: ''

    production:
        dataSources:
            dataSource:
                dbCreate: update
                url: *****************************************************************************************************************
                type: com.zaxxer.hikari.HikariDataSource
                properties:
                    maximumPoolSize: 20
                    minimumIdle: 5
                    connectionTimeout: 30000
                    idleTimeout: 600000
                    maxLifetime: 1800000
                    leakDetectionThreshold: 60000
                    validationTimeout: 5000
                    connectionTestQuery: SELECT 1
            wsuser:
                dbCreate: update
                url: **************************************************************************************************************
                type: com.zaxxer.hikari.HikariDataSource
                properties:
                    maximumPoolSize: 15
                    minimumIdle: 3
                    connectionTimeout: 30000
                    idleTimeout: 600000
                    maxLifetime: 1800000
            wscontent:
                dbCreate: update
                url: *****************************************************************************************************************
                type: com.zaxxer.hikari.HikariDataSource
                properties:
                    maximumPoolSize: 20
                    minimumIdle: 5
                    connectionTimeout: 30000
                    idleTimeout: 600000
                    maxLifetime: 1800000
            wslog:
                dbCreate: update
                url: *************************************************************************************************************
                type: com.zaxxer.hikari.HikariDataSource
                properties:
                    maximumPoolSize: 10
                    minimumIdle: 2
                    connectionTimeout: 30000
                    idleTimeout: 600000
                    maxLifetime: 1800000
        grails:
            serverURL: https://www.wonderslate.com

---
grails:
    plugin:
        springsecurity:
            userLookup:
                userDomainClassName: 'usermanagement.User'
                authorityJoinClassName: 'usermanagement.UserRole'
            authority:
                className: 'usermanagement.Role'
            controllerAnnotations:
                staticRules:
                    - pattern: '/'
                      access: ['permitAll']
                    - pattern: '/error'
                      access: ['permitAll']
                    - pattern: '/books/index'
                      access: ['permitAll']
                    - pattern: '/books/index.gsp'
                      access: ['permitAll']
                    - pattern: '/shutdown'
                      access: ['permitAll']
                    - pattern: '/assets/**'
                      access: ['permitAll']
                    - pattern: '/**/js/**'
                      access: ['permitAll']
                    - pattern: '/**/css/**'
                      access: ['permitAll']
                    - pattern: '/**/images/**'
                      access: ['permitAll']
                    - pattern: '/**/favicon.ico'
                      access: ['permitAll']
                    - pattern: '/*'
                      access: ['permitAll']
                    - pattern: '/**'
                      access: ['permitAll']
            filterChain:
                chainMap:
                    - pattern: '/assets/**'
                      filters: 'none'
                    - pattern: '/**/js/**'
                      filters: 'none'
                    - pattern: '/**/css/**'
                      filters: 'none'
                    - pattern: '/**/images/**'
                      filters: 'none'
                    - pattern: '/**/favicon.ico'
                      filters: 'none'
                    - pattern: '/**'
                      filters: 'JOINED_FILTERS'
                    - pattern: '/api/**'
                      filters: 'JOINED_FILTERS,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'
            rest:
                login:
                    active: true
                    endpointUrl: '/api/login'
                    failureStatusCode: 401
                    useJsonCredentials: true
                    usernamePropertyName: 'username'
                    passwordPropertyName: 'password'
                token:
                    generation:
                        jwt:
                            secret: 'dummySecretKeyThatIsAtLeast32CharactersLongForPluginInit'
                    storage:
                        useJwt: false
                        jwt:
                            secret: 'ChangeThisSecretKeyToSomethingStrong123!'
                        useGorm: true
                        gorm:
                            tokenDomainClassName: 'usermanagement.AuthenticationToken'
                            tokenValuePropertyName: 'token'
                            usernamePropertyName: 'username'
                    validation:
                        active: true
                        headerName: 'X-Auth-Token'
                        endpointUrl: '/api/validate'
                        useBearerToken: false
            rememberMe:
                alwaysRemember: true
            useSecurityEventListener: true
            securityConfigType: 'Annotation'
            rejectIfNoRule: false
            fii:
                rejectPublicInvocations: false
            headers:
                frameOptions: 'SAMEORIGIN'
                contentTypeOptions: true
                xssProtection: '1; mode=block'
                httpStrictTransportSecurity: 'max-age=31536000; includeSubDomains'
mail:
    host: smtp.gmail.com
    port: 465
    username: <EMAIL>
    password: yourpassword
    props:
        mail.smtp.auth: true
        mail.smtp.socketFactory.port: 465
        mail.smtp.socketFactory.class: javax.net.ssl.SSLSocketFactory
        mail.smtp.socketFactory.fallback: false
    overrideAddress: <EMAIL>
    disabled: true
    default:
        from: "Wonderslate <<EMAIL>>"

spring:
    web:
        cors:
            allowed-origins: "*"
            allowed-methods: "GET,HEAD,POST,PUT,DELETE,TRACE,OPTIONS"
            allowed-headers: "origin,authorization,accept,content-type,x-requested-with,x-auth-token"
            allow-credentials: true
            max-age: 3600

cors:
    enabled: true
    url:
        pattern: ['/api/*','/wonderslate/funlearn/*','/**/funlearn/**']

server:
    session:
        timeout: 28800 # 8 hours

quartz:
    autoStartup: true
    jdbcStore: false

wordpress:
    url: http://*************:8888/wordpress/
    blogId: 0
    username: root
    password: root

razorpay:
    keyId: rzp_test_Hs6ZcOvyPgPdJK
    secretKey: 0krPs8xnZhudWnlU6Jiymd9h

processpdf:
    url: http://process.wonderslate.com

# Additional configurations migrated from Config.groovy
grails:
    exceptionresolver:
        params:
            exclude: ['password']
    json:
        legacy:
            builder: false
    enable:
        native2ascii: true
    spring:
        bean:
            packages: []
    web:
        disable:
            multipart: false
    scaffolding:
        templates:
            domainSuffix: 'Instance'

# Environment-specific configurations
environments:
    development:
        uploadFolder: "c:/temp/upload/"
        grails:
            logging:
                jul:
                    usebridge: true
    production:
        uploadFolder: "c:/temp/upload/"
        grails:
            logging:
                jul:
                    usebridge: false

management:
    endpoints:
        web:
            exposure:
                include: health,info
    endpoint:
        health:
            show-details: when-authorized

