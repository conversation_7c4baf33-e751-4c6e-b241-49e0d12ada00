---
grails:
    profile: web
    codegen:
        defaultPackage: wonderslate329
    spring:
        transactionManagement:
            proxies: false
    gorm:
        autowire: true
        reactor:
            events: false
info:
    app:
        name: '@info.app.name@'
        version: '@info.app.version@'
        grailsVersion: '@info.app.grailsVersion@'
spring:
    main:
        banner-mode: "off"
    groovy:
        template:
            check-template-location: false

endpoints:
    enabled: false
    jmx:
        enabled: true

---
grails:
    mime:
        disable:
            accept:
                header:
                    userAgents:
                        - Gecko
                        - WebKit
                        - Presto
                        - Trident
        types:
            all: '*/*'
            atom: application/atom+xml
            css: text/css
            csv: text/csv
            form: application/x-www-form-urlencoded
            html:
                - text/html
                - application/xhtml+xml
            js: text/javascript
            json:
                - application/json
                - text/json
            multipartForm: multipart/form-data
            pdf: application/pdf
            rss: application/rss+xml
            text: text/plain
            hal:
                - application/hal+json
                - application/hal+xml
            xml:
                - text/xml
                - application/xml
    urlmapping:
        cache:
            maxsize: 1000
    controllers:
        defaultScope: singleton
        upload:
            maxFileSize: 80214400
            maxRequestSize: 80214400
    converters:
        encoding: UTF-8
    views:
        default:
            codec: html
        gsp:
            encoding: UTF-8
            htmlcodec: xml
            codecs:
                expression: html
                scriptlets: html
                taglib: none
                staticparts: none
endpoints:
    jmx:
        unique-names: true

---
hibernate:
    cache:
        queries: false
        use_second_level_cache: false
        use_query_cache: false
        region.factory_class: org.hibernate.cache.ehcache.SingletonEhCacheRegionFactory

dataSources:
    dataSource:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        username: wscontent
        password: wscontent
    wsuser:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        username: wsuser
        password: wsuser
    wscontent:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        username: wscontent
        password: wscontent
    wslog:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
        username: wslog
        password: wslog

environments:
    development:
        dataSources:
            dataSource:
                dbCreate: update
                url: ****************************************************************************
            wsuser:
                dbCreate: update
                url: *************************************************************************
            wslog: # Add this
                dbCreate: update
                url: ************************************************************************
        grails:
            serverURL: http://localhost:8080

    test:
        dataSource:
            dbCreate: update
            url: jdbc:h2:mem:testDb;MVCC=TRUE;LOCK_TIMEOUT=10000;DB_CLOSE_ON_EXIT=FALSE

    production:
        dataSource:
            dbCreate: update
            url: ****************************************************************
            properties:
                jmxEnabled: true
                initialSize: 5
                maxActive: 50
                minIdle: 5
                maxIdle: 25
                maxWait: 10000
                maxAge: 600000
                timeBetweenEvictionRunsMillis: 5000
                minEvictableIdleTimeMillis: 60000
                validationQuery: SELECT 1
                validationQueryTimeout: 3
                validationInterval: 15000
                testOnBorrow: true
                testWhileIdle: true
                testOnReturn: false
                jdbcInterceptors: ConnectionState
                defaultTransactionIsolation: 2 # TRANSACTION_READ_COMMITTED
        grails:
            serverURL: https://www.wonderslate.com

---
grails:
    plugin:
        springsecurity:
            userLookup:
                userDomainClassName: 'com.wonderslate.usermanagement.User'
                authorityJoinClassName: 'com.wonderslate.usermanagement.UserRole'
            authority:
                className: 'com.wonderslate.usermanagement.Role'
            controllerAnnotations:
                staticRules:
                    - pattern: '/'
                      access: ['permitAll']
                    - pattern: '/error'
                      access: ['permitAll']
                    - pattern: '/books/index'
                      access: ['permitAll']
                    - pattern: '/books/index.gsp'
                      access: ['permitAll']
                    - pattern: '/shutdown'
                      access: ['permitAll']
                    - pattern: '/assets/**'
                      access: ['permitAll']
                    - pattern: '/**/js/**'
                      access: ['permitAll']
                    - pattern: '/**/css/**'
                      access: ['permitAll']
                    - pattern: '/**/images/**'
                      access: ['permitAll']
                    - pattern: '/**/favicon.ico'
                      access: ['permitAll']
                    - pattern: '/*'
                      access: ['permitAll']
                    - pattern: '/**'
                      access: ['permitAll']
            filterChain:
                chainMap:
                    - pattern: '/assets/**'
                      filters: 'none'
                    - pattern: '/**/js/**'
                      filters: 'none'
                    - pattern: '/**/css/**'
                      filters: 'none'
                    - pattern: '/**/images/**'
                      filters: 'none'
                    - pattern: '/**/favicon.ico'
                      filters: 'none'
                    - pattern: '/**'
                      filters: 'JOINED_FILTERS'
                    - pattern: '/api/**'
                      filters: 'JOINED_FILTERS,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'
            rest:
                login:
                    active: true
                    endpointUrl: '/api/login'
                    failureStatusCode: 401
                    useJsonCredentials: true
                    usernamePropertyName: 'username'
                    passwordPropertyName: 'password'
                token:
                    generation:
                        jwt:
                            secret: 'dummySecretKeyThatIsAtLeast32CharactersLongForPluginInit'
                    storage:
                        useJwt: false
                        jwt:
                            secret: 'ChangeThisSecretKeyToSomethingStrong123!'
                        useGorm: true
                        gorm:
                            tokenDomainClassName: 'com.wonderslate.usermanagement.AuthenticationToken'
                            tokenValuePropertyName: 'token'
                            usernamePropertyName: 'username'
                    validation:
                        active: true
                        headerName: 'X-Auth-Token'
                        endpointUrl: '/api/validate'
                        useBearerToken: false
            rememberMe:
                alwaysRemember: true
            useSecurityEventListener: true
mail:
    host: smtp.gmail.com
    port: 465
    username: <EMAIL>
    password: yourpassword
    props:
        mail.smtp.auth: true
        mail.smtp.socketFactory.port: 465
        mail.smtp.socketFactory.class: javax.net.ssl.SSLSocketFactory
        mail.smtp.socketFactory.fallback: false
    overrideAddress: <EMAIL>
    disabled: true
    default:
        from: "Wonderslate <<EMAIL>>"

cors:
    enabled: true
    url:
        pattern: ['/api/*','/wonderslate/funlearn/*','/**/funlearn/**']
    headers:
        Access-Control-Allow-Origin: "*"
        Access-Control-Allow-Credentials: true
        Access-Control-Allow-Headers: "origin, authorization, accept, content-type, x-requested-with,x-auth-token"
        Access-Control-Allow-Methods: "GET, HEAD, POST, PUT, DELETE, TRACE, OPTIONS"
        Access-Control-Max-Age: 3600

server:
    session:
        timeout: 28800 # 8 hours

quartz:
    autoStartup: true
    jdbcStore: false

wordpress:
    url: http://*************:8888/wordpress/
    blogId: 0
    username: root
    password: root

razorpay:
    keyId: rzp_test_Hs6ZcOvyPgPdJK
    secretKey: 0krPs8xnZhudWnlU6Jiymd9h

processpdf:
    url: http://process.wonderslate.com

