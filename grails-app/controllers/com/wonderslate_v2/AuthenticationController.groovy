package com.wonderslate_v2

import grails.plugin.springsecurity.SpringSecurityService

class AuthenticationController {

    SpringSecurityService springSecurityService

    def index() {
        redirect(action: 'login')
    }

    def login() {
        if (springSecurityService.isLoggedIn()) {
            redirect(controller: 'home', action: 'index')
            return
        }
        [:]
    }

}
