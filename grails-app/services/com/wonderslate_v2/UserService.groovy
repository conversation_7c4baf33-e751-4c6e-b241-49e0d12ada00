package com.wonderslate_v2

import com.wonderslate.usermanagement.User
import grails.gorm.transactions.Transactional

@Transactional('wsuser')
class UserService {

    def springSecurityService

    User findByUsername(String username) {
        return User.wsuser.findByUsername(username)
    }

    User findByEmail(String email) {
        return User.wsuser.findByEmail(email)
    }

    User createUser(String username, String password, String email, String name) {
        // Encode password if encoder is available, otherwise store as plain text
        String encodedPassword = password
        try {
            if (springSecurityService?.passwordEncoder) {
                encodedPassword = springSecurityService.encodePassword(password)
            }
        } catch (Exception e) {
            log.warn("Could not encode password, storing as plain text: ${e.message}")
        }

        User user = new User(
            username: username,
            password: encodedPassword,
            email: email,
            name: name,
            enabled: true,
            accountExpired: false,
            accountLocked: false,
            passwordExpired: false,
            dateCreated: new Date()
        )

        return user.save(flush: true, failOnError: true)
    }

    boolean validatePassword(String plainPassword, String encodedPassword) {
        try {
            // First check if we have a password encoder
            if (!springSecurityService?.passwordEncoder) {
                // Fallback to plain text comparison if no encoder is available
                return plainPassword == encodedPassword
            }

            // Check if the stored password has an encoding prefix (like {bcrypt}, {SHA-256}, etc.)
            if (!encodedPassword?.startsWith('{') && !encodedPassword?.startsWith('$')) {
                // Likely plain text password, compare directly
                return plainPassword == encodedPassword
            }

            // Try to use the password encoder
            return springSecurityService.passwordEncoder.matches(plainPassword, encodedPassword)
        } catch (Exception e) {
            log.error("Error validating password: ${e.message}", e)
            // Fallback to plain text comparison
            return plainPassword == encodedPassword
        }
    }
}
